{% extends "layouts/base.html" %}
{% from "partials/pagination.html" import render_pagination %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ title }}</h1>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        {% if current_user.is_admin %}
          View and manage all employee attendance records
        {% else %}
          View your team's attendance records and approve requests
        {% endif %}
      </p>
    </div>
    <div class="mt-4 sm:mt-0 flex space-x-3">
      {% if current_user.is_admin %}
      <a href="{{ url_for('admin_attendance.list_attendance_records') }}" class="btn btn-outline btn-md">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
        Admin View
      </a>
      {% endif %}
      <a href="{{ url_for('attendance.my_attendance') }}" class="btn btn-primary btn-md">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
        </svg>
        My Attendance
      </a>
    </div>
  </div>

  <!-- Filters -->
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
    <form method="GET" class="space-y-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <!-- Employee Filter -->
        <div>
          <label for="employee" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Employee
          </label>
          <select name="employee" id="employee" class="form-select">
            {% for value, label in filter_form.employee.choices %}
              <option value="{{ value }}" {% if request.args.get('employee') == value %}selected{% endif %}>
                {{ label }}
              </option>
            {% endfor %}
          </select>
        </div>

        <!-- Attendance Type Filter -->
        <div>
          <label for="attendance_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Type
          </label>
          <select name="attendance_type" id="attendance_type" class="form-select">
            {% for value, label in filter_form.attendance_type.choices %}
              <option value="{{ value }}" {% if request.args.get('attendance_type') == value %}selected{% endif %}>
                {{ label }}
              </option>
            {% endfor %}
          </select>
        </div>

        <!-- Status Filter -->
        <div>
          <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Status
          </label>
          <select name="status" id="status" class="form-select">
            {% for value, label in filter_form.status.choices %}
              <option value="{{ value }}" {% if request.args.get('status') == value %}selected{% endif %}>
                {{ label }}
              </option>
            {% endfor %}
          </select>
        </div>

        <!-- Start Date Filter -->
        <div>
          <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Start Date
          </label>
          <input type="date" name="start_date" id="start_date"
                 value="{{ request.args.get('start_date', '') }}"
                 class="form-input">
        </div>

        <!-- End Date Filter -->
        <div>
          <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            End Date
          </label>
          <input type="date" name="end_date" id="end_date"
                 value="{{ request.args.get('end_date', '') }}"
                 class="form-input">
        </div>
      </div>

      <!-- Filter Actions -->
      <div class="flex flex-wrap gap-2">
        <button type="submit" class="btn btn-outline btn-sm">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
          </svg>
          Apply Filters
        </button>
        <a href="{{ url_for('attendance.team_attendance') }}" class="btn btn-outline btn-sm">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Reset
        </a>
        <button type="button" class="btn btn-outline btn-sm">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Export
        </button>
      </div>
    </form>
  </div>

  <!-- Records Table -->
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Employee
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Date
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Type
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Duration
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Status
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Holiday
            </th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          {% for record in attendance_records %}
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-8 w-8">
                  <div class="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {{ record.employee_detail.first_name[0] }}{{ record.employee_detail.last_name[0] }}
                    </span>
                  </div>
                </div>
                <div class="ml-3">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ record.employee_detail.first_name }} {{ record.employee_detail.last_name }}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">
                    {{ record.employee_detail.employee_number }}
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
              {{ record.date.strftime('%Y-%m-%d') }}
              <div class="text-xs text-gray-500 dark:text-gray-400">
                {{ record.date.strftime('%A') }}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900 dark:text-white">{{ record.attendance_type.name }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">{{ record.attendance_type.code }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
              {% if record.attendance_type.is_full_day %}
                Full Day
              {% else %}
                {% if record.duration_hours %}
                  {{ record.duration_hours }}h
                {% endif %}
                {% if record.start_time and record.end_time %}
                  <div class="text-xs text-gray-500 dark:text-gray-400">
                    {{ record.start_time.strftime('%H:%M') }} - {{ record.end_time.strftime('%H:%M') }}
                  </div>
                {% endif %}
              {% endif %}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              {% if record.status == 'Pending' %}
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                  Pending
                </span>
              {% elif record.status == 'Approved' %}
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                  Approved
                </span>
              {% elif record.status == 'Rejected' %}
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                  Rejected
                </span>
              {% elif record.status == 'Auto-Approved' %}
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  Auto Approved
                </span>
              {% endif %}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
              {# Holiday indicator will be added in Task 2 #}
              <span class="text-gray-400 dark:text-gray-500">-</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
              {% if record.status == 'Pending' and (current_user.is_admin or current_user.is_manager) %}
                <button
                  onclick="approveRecord({{ record.id }})"
                  class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                  Approve
                </button>
                <button
                  onclick="rejectRecord({{ record.id }})"
                  class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                  Reject
                </button>
              {% else %}
                <span class="text-gray-400 dark:text-gray-500">-</span>
              {% endif %}
            </td>
          </tr>
          {% else %}
          <tr>
            <td colspan="7" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
              No attendance records found for your team.
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    {% if pagination.total_pages > 1 %}
    <div class="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700">
      {{ render_pagination(pagination) }}
    </div>
    {% endif %}
  </div>
</div>

<script>
function approveRecord(recordId) {
  if (confirm('Are you sure you want to approve this attendance record?')) {
    fetch(`{{ url_for('admin_attendance.approve_attendance_record', record_id=0) }}`.replace('0', recordId), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-CSRFToken': '{{ csrf_token() }}'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showToast(data.message, 'success');
        location.reload();
      } else {
        showToast(data.message, 'error');
      }
    })
    .catch(error => {
      showToast('An error occurred while approving the record.', 'error');
    });
  }
}

function rejectRecord(recordId) {
  const reason = prompt('Please provide a reason for rejection:');
  if (reason && reason.trim()) {
    fetch(`{{ url_for('admin_attendance.reject_attendance_record', record_id=0) }}`.replace('0', recordId), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-CSRFToken': '{{ csrf_token() }}'
      },
      body: `rejection_reason=${encodeURIComponent(reason)}`
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showToast(data.message, 'success');
        location.reload();
      } else {
        showToast(data.message, 'error');
      }
    })
    .catch(error => {
      showToast('An error occurred while rejecting the record.', 'error');
    });
  }
}
</script>
{% endblock %}
