{% extends "base.html" %}
{% from "partials/pagination.html" import render_pagination %}
{% from "partials/forms/base_form.html" import form_group %}
{% from "partials/components/button_group.html" import button_group %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="space-y-6">
  <!-- Page Header -->
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ title }}</h1>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        View and manage your attendance records
      </p>
    </div>
    <div class="mt-4 sm:mt-0 flex space-x-3">
      <a href="{{ url_for('attendance.attendance_calendar') }}" class="btn btn-outline btn-md">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
        Calendar View
      </a>
      <a href="{{ url_for('attendance.request_attendance') }}" class="btn btn-primary btn-md">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
        Request Attendance
      </a>
    </div>
  </div>

  <!-- Filter Section -->
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">Filter Attendance Records</h3>
    </div>
    <div class="card-content">
      <form method="GET" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- Attendance Type Filter -->
          {{ form_group(
            label="Attendance Type",
            name="attendance_type",
            type="select",
            value=request.args.get('attendance_type', ''),
            options=filter_form.attendance_type.choices
          ) }}

          <!-- Status Filter -->
          {{ form_group(
            label="Status",
            name="status",
            type="select",
            value=request.args.get('status', ''),
            options=filter_form.status.choices
          ) }}

          <!-- Start Date Filter -->
          {{ form_group(
            label="Start Date",
            name="start_date",
            type="date",
            value=request.args.get('start_date', '')
          ) }}

          <!-- End Date Filter -->
          {{ form_group(
            label="End Date",
            name="end_date",
            type="date",
            value=request.args.get('end_date', '')
          ) }}
        </div>

        <!-- Filter Actions -->
        <div class="flex justify-end gap-2">
          {% set filter_buttons = [
            {"text": "Reset", "variant": "outline", "href": url_for('attendance.my_attendance'), "icon": "rotate-ccw"},
            {"text": "Apply Filters", "variant": "primary", "type": "submit", "icon": "filter"}
          ] %}
          {{ button_group(filter_buttons) }}
        </div>
      </form>
    </div>
  </div>

  <!-- Records Table -->
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Date
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Type
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Duration
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Status
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Holiday
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Notes
            </th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          {% for record in attendance_records %}
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
              {{ record.date.strftime('%Y-%m-%d') }}
              <div class="text-xs text-gray-500 dark:text-gray-400">
                {{ record.date.strftime('%A') }}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900 dark:text-white">{{ record.attendance_type.name }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">{{ record.attendance_type.code }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
              {% if record.attendance_type.is_full_day %}
                Full Day
              {% else %}
                {% if record.duration_hours %}
                  {{ record.duration_hours }}h
                {% endif %}
                {% if record.start_time and record.end_time %}
                  <div class="text-xs text-gray-500 dark:text-gray-400">
                    {{ record.start_time.strftime('%H:%M') }} - {{ record.end_time.strftime('%H:%M') }}
                  </div>
                {% endif %}
              {% endif %}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              {% if record.status == 'Pending' %}
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                  Pending
                </span>
              {% elif record.status == 'Approved' %}
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                  Approved
                </span>
              {% elif record.status == 'Rejected' %}
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                  Rejected
                </span>
              {% elif record.status == 'Auto-Approved' %}
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  Auto Approved
                </span>
              {% endif %}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
              {# Holiday indicator will be added in Task 2 #}
              <span class="text-gray-400 dark:text-gray-500">-</span>
            </td>
            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
              {% if record.notes %}
                <div class="max-w-xs truncate" title="{{ record.notes }}">
                  {{ record.notes }}
                </div>
              {% else %}
                <span class="text-gray-400 dark:text-gray-500">-</span>
              {% endif %}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
              {% if record.status == 'pending' %}
                <a href="{{ url_for('attendance.edit_attendance', record_id=record.id) }}"
                   class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300">
                  Edit
                </a>
                <button
                  onclick="deleteRecord({{ record.id }}, '{{ record.date.strftime('%Y-%m-%d') }}')"
                  class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                  Delete
                </button>
              {% else %}
                <span class="text-gray-400 dark:text-gray-500">-</span>
              {% endif %}
            </td>
          </tr>
          {% else %}
          <tr>
            <td colspan="7" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
              No attendance records found.
              <a href="{{ url_for('attendance.request_attendance') }}" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400">
                Create your first attendance request
              </a>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    {% if pagination.total_pages > 1 %}
    <div class="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700">
      {{ render_pagination(pagination) }}
    </div>
    {% endif %}
  </div>
</div>

<script>
function deleteRecord(recordId, recordDate) {
  if (confirm(`Are you sure you want to delete your attendance request for ${recordDate}?`)) {
    fetch(`{{ url_for('attendance.delete_attendance', record_id=0) }}`.replace('0', recordId), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-CSRFToken': '{{ csrf_token() }}'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showToast(data.message, 'success');
        location.reload();
      } else {
        showToast(data.message, 'error');
      }
    })
    .catch(error => {
      showToast('An error occurred while deleting the record.', 'error');
    });
  }
}
</script>
{% endblock %}
